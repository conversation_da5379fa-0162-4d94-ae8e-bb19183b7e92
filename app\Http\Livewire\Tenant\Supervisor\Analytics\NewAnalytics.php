<?php

namespace App\Http\Livewire\Tenant\Supervisor\Analytics;

use App\Models\Tenant\Resource;
use App\Models\Tenant\ResourceAction;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Str;
use Livewire\Component;
use Illuminate\Support\Facades\Log;

class NewAnalytics extends Component
{
    // Filter Properties - Matching existing analytics exactly
    public $selectedDateFilter = '30 Days';
    public $selectedChannel = '';
    public $fromDate = '';
    public $toDate = '';
    public $showFilterPopup = false;

    // Additional properties to match existing analytics structure
    public $filterType = 'voice'; // 'voice' or 'non-voice' to match supervisor analytics
    public $social = 'All'; // Social media channel filter to match existing analytics

    // Filter Methods
    public function updatedSelectedDateFilter()
    {
        // Only reset custom dates if a predefined filter is selected
        if ($this->selectedDateFilter && $this->selectedDateFilter !== '') {
            $this->resetCustomDates();
        }
        $this->refreshData();
    }

    public function updatedSelectedChannel()
    {
        // Update filterType and social based on selectedChannel to match existing analytics
        if ($this->selectedChannel === 'Voice') {
            $this->filterType = 'voice';
            $this->social = 'All'; // Voice doesn't use social media channels
            Log::info('Voice channel selected, clearing social media filters');
        } elseif ($this->selectedChannel === 'Non-Voice') {
            $this->filterType = 'non-voice';
            $this->social = 'All'; // Non-Voice selected but no specific channel
            Log::info('Non-Voice channel selected');
        } elseif (
            !empty($this->selectedChannel) &&
            !in_array($this->selectedChannel, ['Voice', 'Non-Voice', ''])
        ) {
            // Specific social media channel selected
            $this->filterType = 'non-voice';
            $this->social = $this->selectedChannel;
            Log::info('Specific social media channel selected: ' . $this->selectedChannel);
        } else {
            // All channels selected (empty selectedChannel means "All")
            $this->filterType = 'non-voice'; // Default to non-voice for "All" to show ResourceAction data
            $this->social = 'All';
        }

        $this->refreshData();
    }

    public function updatedFromDate()
    {
        // Clear predefined date filter when custom dates are used
        if ($this->fromDate && $this->toDate) {
            $this->selectedDateFilter = '';
        }
        if ($this->fromDate && $this->toDate) {
            $this->refreshData();
        }
    }

    public function updatedToDate()
    {
        // Clear predefined date filter when custom dates are used
        if ($this->fromDate && $this->toDate) {
            $this->selectedDateFilter = '';
        }
        if ($this->fromDate && $this->toDate) {
            $this->refreshData();
        }
    }

    public function toggleFilterPopup()
    {
        $this->showFilterPopup = !$this->showFilterPopup;
    }

    public function resetFilters()
    {
        $this->selectedDateFilter = '30 Days';
        $this->selectedChannel = '';
        $this->filterType = 'voice';
        $this->social = 'All';
        $this->fromDate = '';
        $this->toDate = '';
        $this->showFilterPopup = false;
        $this->refreshData();
    }

    private function resetCustomDates()
    {
        $this->fromDate = '';
        $this->toDate = '';
    }

    public function applyFilters()
    {
        $this->showFilterPopup = false;
        $this->refreshData();
    }

    private function refreshData()
    {
        // Emit event to refresh chart with new data
        $this->emit('refreshReceivedTrendChart', $this->getReceivedTrendData());
    }

    // Received Trend Data with filtering logic matching existing analytics exactly
    public function getReceivedTrendData()
    {
        // Determine date range
        $dateRange = $this->getDateRange();
        $fromDate = $dateRange['from'];
        $toDate = $dateRange['to'];

        // Use the EXACT same logic as existing analytics for received trend data
        if ($this->social !== null && $this->social !== 'All' && $this->social !== '') {
            // For specific social media channels: Use ResourceAction with joins (matching existing analytics)
            $data = ResourceAction::join('resources', 'resource_actions.resource_id', '=', 'resources.id')
                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
                ->where('resource_actions.action', 'New')
                ->where('resource_infos.channel', '=', $this->social)
                ->where('resource_actions.created_at', '>=', $fromDate)
                ->where('resource_actions.created_at', '<=', $toDate)
                ->get();
        } else {
            // For all channels or no specific filter: Use ResourceAction with 'New' action (matching existing analytics)
            $data = ResourceAction::where('action', 'New')
                ->where('created_at', '>=', $fromDate)
                ->where('created_at', '<=', $toDate)
                ->get();
        }

        // Debug: Log query details (matching existing analytics structure)
        Log::info('Query details:', [
            'selectedChannel' => $this->selectedChannel,
            'filterType' => $this->filterType,
            'social' => $this->social,
            'fromDate' => $fromDate->format('Y-m-d H:i:s'),
            'toDate' => $toDate->format('Y-m-d H:i:s'),
            'recordCount' => $data->count(),
            'customDateRange' => !empty($this->fromDate) && !empty($this->toDate)
        ]);

        // Initialize counters
        $rec_t_sat = 0;
        $rec_t_sun = 0;
        $rec_t_mon = 0;
        $rec_t_tue = 0;
        $rec_t_wed = 0;
        $rec_t_thu = 0;
        $rec_t_fri = 0;

        // Count by day of week
        foreach ($data as $dat) {
            $dayName = $dat->created_at->format('l');
            switch ($dayName) {
                case 'Saturday':
                    $rec_t_sat++;
                    break;
                case 'Sunday':
                    $rec_t_sun++;
                    break;
                case 'Monday':
                    $rec_t_mon++;
                    break;
                case 'Tuesday':
                    $rec_t_tue++;
                    break;
                case 'Wednesday':
                    $rec_t_wed++;
                    break;
                case 'Thursday':
                    $rec_t_thu++;
                    break;
                case 'Friday':
                    $rec_t_fri++;
                    break;
            }
        }

        return [
            'sat' => $rec_t_sat,
            'sun' => $rec_t_sun,
            'mon' => $rec_t_mon,
            'tue' => $rec_t_tue,
            'wed' => $rec_t_wed,
            'thu' => $rec_t_thu,
            'fri' => $rec_t_fri,
        ];
    }

    private function getDateRange()
    {
        // If custom date range is provided, use it (matching existing analytics)
        if (!empty($this->fromDate) && !empty($this->toDate)) {
            return [
                'from' => Carbon::parse($this->fromDate)->startOfDay(),
                'to' => Carbon::parse($this->toDate)->endOfDay()
            ];
        }

        // Otherwise, use predefined date filter (matching existing analytics exactly)
        switch ($this->selectedDateFilter) {
            case '24 Hours':
                return [
                    'from' => Carbon::now()->subHours(24),
                    'to' => Carbon::now()
                ];
            case '7 Days':
                return [
                    'from' => Carbon::now()->subDays(7),
                    'to' => Carbon::now()
                ];
            case '30 Days':
                return [
                    'from' => Carbon::now()->subDays(30),
                    'to' => Carbon::now()
                ];
            case '60 Days':
                return [
                    'from' => Carbon::now()->subDays(60),
                    'to' => Carbon::now()
                ];
            default:
                // Default to 30 days (matching existing analytics)
                return [
                    'from' => Carbon::now()->subDays(30),
                    'to' => Carbon::now()
                ];
        }
    }

    public function mount()
    {
        // Initialize with default values (matching existing analytics)
        $this->selectedDateFilter = '30 Days';
        $this->selectedChannel = ''; // Empty means "All"
        $this->filterType = 'non-voice'; // Default to non-voice to show ResourceAction data like existing analytics
        $this->social = 'All';

        // Get initial data and emit to chart
        $data = $this->getReceivedTrendData();

        // Debug: Log the data to see what we're getting
        Log::info('Initial Received Trend Data:', $data);

        // Emit initial data to the chart when component mounts
        $this->emit('refreshReceivedTrendChart', $data);
    }

    public function hydrate()
    {
        // This runs after every Livewire request
        // Ensure data is refreshed after any property changes
        $this->emit('refreshReceivedTrendChart', $this->getReceivedTrendData());
    }

    public function render()
    {
        return view('livewire.tenant.supervisor.analytics.new-analytics');
    }
}
