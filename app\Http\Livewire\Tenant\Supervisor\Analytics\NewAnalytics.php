<?php

namespace App\Http\Livewire\Tenant\Supervisor\Analytics;

use App\Models\Tenant\Resource;
use App\Models\Tenant\ResourceAction;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Str;
use Livewire\Component;
use Illuminate\Support\Facades\Log;

class NewAnalytics extends Component
{
    // Filter Properties - Matching existing analytics exactly
    public $selectedDateFilter = '24 Hours';
    public $selectedChannel = '';
    public $fromDate = '';
    public $toDate = '';
    public $showFilterPopup = false;

    // Additional properties to match existing analytics structure
    public $filterType = 'voice'; // 'voice' or 'non-voice' to match supervisor analytics
    public $social = 'All'; // Social media channel filter to match existing analytics

    // Total tickets count
    public $totalReceivedTickets = 0;

    // Percentage calculations
    public $activeTicketsPercentage = 0;
    public $totalActiveTickets = 0;

    // Filter Methods
    public function updatedSelectedDateFilter()
    {
        // Only reset custom dates if a predefined filter is selected
        if ($this->selectedDateFilter && $this->selectedDateFilter !== '') {
            $this->resetCustomDates();
        }
        $this->refreshData();
    }

    public function updatedSelectedChannel()
    {
        // Update filterType and social based on selectedChannel to match existing analytics
        if ($this->selectedChannel === 'Voice') {
            $this->filterType = 'voice';
            $this->social = 'All'; // Voice doesn't use social media channels
            Log::info('Voice channel selected, clearing social media filters');
        } elseif ($this->selectedChannel === 'Non-Voice') {
            $this->filterType = 'non-voice';
            $this->social = 'All'; // Non-Voice selected but no specific channel
            Log::info('Non-Voice channel selected');
        } elseif (
            !empty($this->selectedChannel) &&
            !in_array($this->selectedChannel, ['Voice', 'Non-Voice', ''])
        ) {
            // Specific social media channel selected
            $this->filterType = 'non-voice';
            $this->social = $this->selectedChannel;
            Log::info('Specific social media channel selected: ' . $this->selectedChannel);
        } else {
            // All channels selected (empty selectedChannel means "All")
            $this->filterType = 'non-voice'; // Default to non-voice for "All" to show ResourceAction data
            $this->social = 'All';
        }

        $this->refreshData();
    }

    public function updatedFromDate()
    {
        // Clear predefined date filter when custom dates are used
        if ($this->fromDate && $this->toDate) {
            $this->selectedDateFilter = '';
        }
        if ($this->fromDate && $this->toDate) {
            $this->refreshData();
        }
    }

    public function updatedToDate()
    {
        // Clear predefined date filter when custom dates are used
        if ($this->fromDate && $this->toDate) {
            $this->selectedDateFilter = '';
        }
        if ($this->fromDate && $this->toDate) {
            $this->refreshData();
        }
    }

    public function toggleFilterPopup()
    {
        $this->showFilterPopup = !$this->showFilterPopup;
    }

    public function resetFilters()
    {
        $this->selectedDateFilter = '24 Hours';
        $this->selectedChannel = ''; // Empty means "All"
        $this->filterType = 'non-voice'; // Default to non-voice to show ResourceAction data like existing analytics
        $this->social = 'All';
        $this->fromDate = '';
        $this->toDate = '';
        $this->showFilterPopup = false;
        $this->refreshData();
    }

    private function resetCustomDates()
    {
        $this->fromDate = '';
        $this->toDate = '';
    }

    public function applyFilters()
    {
        $this->showFilterPopup = false;
        $this->refreshData();
    }

    // Test method to manually trigger chart update
    public function testChartUpdate()
    {
        $testData = [
            'sat' => 10,
            'sun' => 20,
            'mon' => 15,
            'tue' => 25,
            'wed' => 30,
            'thu' => 18,
            'fri' => 22,
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
            'grouping' => 'month',
            'daysDiff' => 60
        ];

        Log::info('Manual test chart update triggered:', $testData);
        $this->emit('refreshReceivedTrendChart', $testData);
        $this->emit('testEvent', 'Manual test triggered with month labels!');
    }

    private function refreshData()
    {
        // Get the chart data first
        $data = $this->getReceivedTrendData();

        // Update total tickets count
        $this->totalReceivedTickets = $this->getTotalReceivedTickets();

        // Update active tickets data
        $activeData = $this->getActiveTicketsData();
        $this->totalActiveTickets = $activeData['count'];
        $this->activeTicketsPercentage = $activeData['percentage'];

        // Log what we're about to emit
        Log::info('Refreshing chart data:', $data);
        Log::info('Analytics data updated: Total=' . $this->totalReceivedTickets . ', Active=' . $this->totalActiveTickets . ' (' . $this->activeTicketsPercentage . '%)');

        // Emit event to refresh chart with new data
        $this->emit('refreshReceivedTrendChart', $data);
    }

    // Received Trend Data with filtering logic matching existing analytics exactly
    public function getReceivedTrendData()
    {
        // Determine date range
        $dateRange = $this->getDateRange();
        $fromDate = $dateRange['from'];
        $toDate = $dateRange['to'];

        // Use the EXACT same logic as existing analytics for received trend data
        if ($this->social !== null && $this->social !== 'All' && $this->social !== '') {
            // For specific social media channels: Use ResourceAction with joins (matching existing analytics)
            $query = ResourceAction::join('resources', 'resource_actions.resource_id', '=', 'resources.id')
                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
                ->where('resource_actions.action', 'New')
                ->where('resource_infos.channel', '=', $this->social)
                ->where('resource_actions.created_at', '>=', $fromDate)
                ->where('resource_actions.created_at', '<=', $toDate);

            Log::info('Social media filtered query SQL:', [
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings()
            ]);

            $data = $query->get();
        } else {
            // For all channels or no specific filter: Use ResourceAction with 'New' action (matching existing analytics)
            $query = ResourceAction::where('action', 'New')
                ->where('created_at', '>=', $fromDate)
                ->where('created_at', '<=', $toDate);

            Log::info('All channels query SQL:', [
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings()
            ]);

            $data = $query->get();
        }

        // Calculate the number of days in the range for dynamic grouping
        $daysDiff = $fromDate->diffInDays($toDate);

        // Debug: Log query details (matching existing analytics structure)
        Log::info('Query details:', [
            'selectedChannel' => $this->selectedChannel,
            'filterType' => $this->filterType,
            'social' => $this->social,
            'fromDate' => $fromDate->format('Y-m-d H:i:s'),
            'toDate' => $toDate->format('Y-m-d H:i:s'),
            'recordCount' => $data->count(),
            'daysDiff' => $daysDiff,
            'customDateRange' => !empty($this->fromDate) && !empty($this->toDate)
        ]);

        // Initialize counters
        $rec_t_sat = 0;
        $rec_t_sun = 0;
        $rec_t_mon = 0;
        $rec_t_tue = 0;
        $rec_t_wed = 0;
        $rec_t_thu = 0;
        $rec_t_fri = 0;

        // Count by day of week
        foreach ($data as $dat) {
            $dayName = $dat->created_at->format('l');
            switch ($dayName) {
                case 'Saturday':
                    $rec_t_sat++;
                    break;
                case 'Sunday':
                    $rec_t_sun++;
                    break;
                case 'Monday':
                    $rec_t_mon++;
                    break;
                case 'Tuesday':
                    $rec_t_tue++;
                    break;
                case 'Wednesday':
                    $rec_t_wed++;
                    break;
                case 'Thursday':
                    $rec_t_thu++;
                    break;
                case 'Friday':
                    $rec_t_fri++;
                    break;
            }
        }

        // Choose grouping and labels based on date range
        if ($daysDiff <= 7) {
            // For 1-7 days: Group by day of week
            $labels = ['SAT', 'SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI'];
            $grouping = 'dayOfWeek';
            // Use existing day-of-week counting logic
        } elseif ($daysDiff <= 31) {
            // For 8-31 days: Group by week
            $labels = [];
            $weekCounts = [];
            $current = $fromDate->copy()->startOfWeek();
            $weekIndex = 0;

            // Create week buckets
            while ($current->lte($toDate) && $weekIndex < 7) {
                $weekEnd = $current->copy()->endOfWeek();
                if ($weekEnd->gt($toDate)) {
                    $weekEnd = $toDate->copy();
                }

                $labels[] = 'W' . ($weekIndex + 1);
                $weekCounts[$weekIndex] = 0;
                $current->addWeek();
                $weekIndex++;
            }

            // Count data by week
            foreach ($data as $dat) {
                $dataDate = $dat->created_at;
                $weekNumber = $fromDate->diffInWeeks($dataDate);
                if ($weekNumber < count($weekCounts)) {
                    $weekCounts[$weekNumber]++;
                }
            }

            // Pad to 7 values and reassign to day variables for chart compatibility
            while (count($weekCounts) < 7) {
                $weekCounts[] = 0;
                $labels[] = '';
            }

            $rec_t_sat = $weekCounts[0] ?? 0;
            $rec_t_sun = $weekCounts[1] ?? 0;
            $rec_t_mon = $weekCounts[2] ?? 0;
            $rec_t_tue = $weekCounts[3] ?? 0;
            $rec_t_wed = $weekCounts[4] ?? 0;
            $rec_t_thu = $weekCounts[5] ?? 0;
            $rec_t_fri = $weekCounts[6] ?? 0;

            $grouping = 'week';
        } else {
            // For 32+ days: Group by month
            $labels = [];
            $monthCounts = [];
            $current = $fromDate->copy()->startOfMonth();
            $monthIndex = 0;

            // Create month buckets
            while ($current->lte($toDate) && $monthIndex < 7) {
                $monthEnd = $current->copy()->endOfMonth();
                if ($monthEnd->gt($toDate)) {
                    $monthEnd = $toDate->copy();
                }

                $labels[] = $current->format('M');
                $monthCounts[$monthIndex] = 0;
                $current->addMonth();
                $monthIndex++;
            }

            // Count data by month
            foreach ($data as $dat) {
                $dataDate = $dat->created_at;
                $monthNumber = $fromDate->diffInMonths($dataDate);
                if ($monthNumber < count($monthCounts)) {
                    $monthCounts[$monthNumber]++;
                }
            }

            // Pad to 7 values and reassign to day variables for chart compatibility
            while (count($monthCounts) < 7) {
                $monthCounts[] = 0;
                $labels[] = '';
            }

            $rec_t_sat = $monthCounts[0] ?? 0;
            $rec_t_sun = $monthCounts[1] ?? 0;
            $rec_t_mon = $monthCounts[2] ?? 0;
            $rec_t_tue = $monthCounts[3] ?? 0;
            $rec_t_wed = $monthCounts[4] ?? 0;
            $rec_t_thu = $monthCounts[5] ?? 0;
            $rec_t_fri = $monthCounts[6] ?? 0;

            $grouping = 'month';
        }

        $result = [
            'sat' => $rec_t_sat,
            'sun' => $rec_t_sun,
            'mon' => $rec_t_mon,
            'tue' => $rec_t_tue,
            'wed' => $rec_t_wed,
            'thu' => $rec_t_thu,
            'fri' => $rec_t_fri,
            'labels' => $labels,
            'grouping' => $grouping,
            'daysDiff' => $daysDiff
        ];

        // Debug: Log the final result
        Log::info('Received Trend Data Result:', $result);
        Log::info('Total records found: ' . $data->count());

        return $result;
    }

    // Calculate total received tickets based on current filters
    public function getTotalReceivedTickets()
    {
        // Determine date range
        $dateRange = $this->getDateRange();
        $fromDate = $dateRange['from'];
        $toDate = $dateRange['to'];

        // Use the EXACT same logic as getReceivedTrendData for consistency
        if ($this->social !== null && $this->social !== 'All' && $this->social !== '') {
            // For specific social media channels: Use ResourceAction with joins
            $query = ResourceAction::join('resources', 'resource_actions.resource_id', '=', 'resources.id')
                ->join('resource_infos', 'resources.id', '=', 'resource_infos.resource_id')
                ->where('resource_actions.action', 'New')
                ->where('resource_infos.channel', '=', $this->social)
                ->where('resource_actions.created_at', '>=', $fromDate)
                ->where('resource_actions.created_at', '<=', $toDate);

            $total = $query->count();
        } else {
            // For all channels or no specific filter: Use ResourceAction with 'New' action
            $total = ResourceAction::where('action', 'New')
                ->where('created_at', '>=', $fromDate)
                ->where('created_at', '<=', $toDate)
                ->count();
        }

        Log::info('Total received tickets calculated:', [
            'total' => $total,
            'fromDate' => $fromDate->format('Y-m-d H:i:s'),
            'toDate' => $toDate->format('Y-m-d H:i:s'),
            'social' => $this->social,
            'selectedChannel' => $this->selectedChannel
        ]);

        return $total;
    }

    // Calculate active tickets percentage
    public function getActiveTicketsData()
    {
        // Get date range
        $dateRange = $this->getDateRange();
        $fromDate = $dateRange['from'];
        $toDate = $dateRange['to'];

        // Get total tickets in the date range
        $totalTickets = $this->getTotalReceivedTickets();

        // Get active tickets (New, In Progress, Pending - not Resolved or Closed)
        $activeTickets = ResourceAction::where('action', 'New')
            ->where('created_at', '>=', $fromDate)
            ->where('created_at', '<=', $toDate)
            ->whereHas('resource', function ($query) {
                $query->whereNotIn('status', ['Resolved', 'Closed']);
            })
            ->count();

        // Calculate percentage
        $percentage = $totalTickets > 0 ? round(($activeTickets / $totalTickets) * 100, 1) : 0;

        Log::info('Active tickets calculated:', [
            'activeTickets' => $activeTickets,
            'totalTickets' => $totalTickets,
            'percentage' => $percentage,
            'fromDate' => $fromDate->format('Y-m-d H:i:s'),
            'toDate' => $toDate->format('Y-m-d H:i:s')
        ]);

        return [
            'count' => $activeTickets,
            'percentage' => $percentage
        ];
    }


    private function getDateRange()
    {
        // If custom date range is provided, use it (matching existing analytics)
        if (!empty($this->fromDate) && !empty($this->toDate)) {
            $range = [
                'from' => Carbon::parse($this->fromDate)->startOfDay(),
                'to' => Carbon::parse($this->toDate)->endOfDay()
            ];
            Log::info('Using custom date range:', [
                'from' => $range['from']->format('Y-m-d H:i:s'),
                'to' => $range['to']->format('Y-m-d H:i:s')
            ]);
            return $range;
        }

        // Otherwise, use predefined date filter (matching existing analytics exactly)
        $range = null;
        switch ($this->selectedDateFilter) {
            case '24 Hours':
                $range = [
                    'from' => Carbon::now()->subHours(24),
                    'to' => Carbon::now()
                ];
                break;
            case '7 Days':
                $range = [
                    'from' => Carbon::now()->subDays(7),
                    'to' => Carbon::now()
                ];
                break;
            case '30 Days':
                $range = [
                    'from' => Carbon::now()->subDays(30),
                    'to' => Carbon::now()
                ];
                break;
            case '60 Days':
                $range = [
                    'from' => Carbon::now()->subDays(60),
                    'to' => Carbon::now()
                ];
                break;
            default:
                // Default to 24 hours (updated default)
                $range = [
                    'from' => Carbon::now()->subHours(24),
                    'to' => Carbon::now()
                ];
                break;
        }

        Log::info('Using predefined date filter:', [
            'filter' => $this->selectedDateFilter,
            'from' => $range['from']->format('Y-m-d H:i:s'),
            'to' => $range['to']->format('Y-m-d H:i:s')
        ]);

        return $range;
    }

    public function mount()
    {
        // Initialize with default values (matching existing analytics)
        $this->selectedDateFilter = '24 Hours';
        $this->selectedChannel = ''; // Empty means "All"
        $this->filterType = 'non-voice'; // Default to non-voice to show ResourceAction data like existing analytics
        $this->social = 'All';

        // Get initial data and emit to chart
        $data = $this->getReceivedTrendData();

        // Initialize total tickets count
        $this->totalReceivedTickets = $this->getTotalReceivedTickets();

        // Initialize active tickets data
        $activeData = $this->getActiveTicketsData();
        $this->totalActiveTickets = $activeData['count'];
        $this->activeTicketsPercentage = $activeData['percentage'];

        Log::info('Initial Received Trend Data:', $data);
        Log::info('Initial Analytics Data: Total=' . $this->totalReceivedTickets . ', Active=' . $this->totalActiveTickets . ' (' . $this->activeTicketsPercentage . '%)');

        // Emit initial data to the chart when component mounts
        $this->emit('refreshReceivedTrendChart', $data);

        // Also emit a simple test event to verify events are working
        $this->emit('testEvent', 'Hello from mount method!');
    }

    public function hydrate()
    {
        // This runs after every Livewire request
        // Only refresh data if needed, not on every request to avoid performance issues
        // The refreshData() method will be called by the updated* methods when filters change
    }

    public function render()
    {
        return view('livewire.tenant.supervisor.analytics.new-analytics');
    }
}
