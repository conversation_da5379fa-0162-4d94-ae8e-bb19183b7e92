@extends('tenant.layouts.supervisor')

@section('title', 'New Analytics')

@section('filter')
    <div style="display: inline-block;">
        <!-- Icon box container -->
        {{-- <div id="iconBox" class="bg-white rounded shadow d-flex justify-content-between align-items-center position-absolute translate-middle-x z-index-1050 d-none"> --}}
        <div id="iconBox"
            class="d-flex justify-content-between align-items-center position-absolute translate-middle-x z-index-1050"
            style="margin: -13px ;">

            <a href="{{ route('supervisor.chatbotStatistics.index') }}" class="icons position-relative me-3"
                onclick="setActive(this)" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="Chat Bot">
                <div class="arrow-top d-none" style="left: 7px;"><img src="{{ asset('img/menu_shape.png') }}" alt=""
                        width="10" style="transform: rotate(270deg);"></div>
                <img id='none-voice' src="{{ global_asset('tenancy/assets/img/chatbot_stats.png') }}" alt="mdo"
                    width="22" height="22">
            </a>
            <div class="icons position-relative me-3" onclick="setActive(this)" data-bs-toggle="tooltip"
                data-bs-placement="bottom" data-bs-title="Analytics">
                <div class="arrow-top" style="left: 7px;"><img src="{{ asset('img/menu_shape.png') }}" alt=""
                        width="10" style="transform: rotate(270deg);"></div>
                <img id='none-voice' src="{{ asset('img/analysis.png') }}" alt="mdo" width="22" height="22">
            </div>
            <a href="{{ route('supervisor.analytics.new') }}" class="icons position-relative me-3" onclick="setActive(this)"
                data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="New Analytics">
                <div class="arrow-top d-none" style="left: 7px;"><img src="{{ asset('img/menu_shape.png') }}" alt=""
                        width="10" style="transform: rotate(270deg);"></div>
                <img id='new-analytics' src="{{ asset('img/analysis.png') }}" alt="New Analytics" width="22"
                    height="22">
            </a>
        </div>

        <!-- Toggle Button -->
        {{-- <div class=" d-flex align-items-center" style="cursor:pointer" onclick="toggleIcons()" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="Type">
            <div>
                <img class="text-success me-1" src="{{asset('img/arraow-dashboard-2.gif')}}" alt="" width="40px" height="40px">
            </div>             --}}
        {{-- <span class="text-success font-weight-medium">Types</span>
            <i class="fas fa-chevron-up text-success ms-1"></i> --}}
        {{-- </div> --}}
    </div>
@endsection

@section('style')
    <style>
        .form-control:focus {
            color: #212529;
            background-color: #f8fafc;
            border-color: #12800a !important;
            outline: 0;
            box-shadow: 0 0 0 .25rem rgb(31, 196, 6, 0.25) !important
        }

        .filter-popup {
            position: absolute;
            z-index: 999;
            background: white;
            border: none;
        }

        /* Analytics Date Dropdown */
        .analytics-date-dropdown {
            position: relative;
            display: inline-block;
        }

        .analytics-date-btn {
            background: white;
            border: 1px solid #e0e0e0;
            padding: 11px 13px;
            height: 44px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #6c757d;
            transition: all 0.2s;
        }

        .analytics-date-btn:hover {
            border-color: #00a34e;
            color: #00a34e;
        }

        .analytics-dropdown-arrow {
            transition: transform 0.2s;
        }

        .analytics-date-btn.has-custom-range {
            border-color: #00a34e;
            color: #00a34e;
            font-weight: 500;
        }

        .analytics-date-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            padding: 8px;
            display: none;
        }

        .analytics-date-menu.show {
            display: block;
        }

        .analytics-date-item {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .analytics-date-item:hover {
            background-color: #f8f9fa;
        }

        .analytics-date-divider {
            height: 1px;
            background-color: #e9ecef;
            margin: 4px 0;
        }

        /* Analytics Filter Modal */
        .analytics-filter-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1050;
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            padding: 80px 50px 20px 20px;
        }

        .analytics-filter-modal {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 400px;
            max-width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
        }

        .analytics-filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px 0 20px;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 20px;
        }

        .analytics-filter-title {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: #333;
        }

        .analytics-filter-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .analytics-filter-close:hover {
            background-color: #f8f9fa;
        }

        .analytics-filter-section {
            padding: 0 20px 20px 20px;
        }

        .analytics-filter-section-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .analytics-filter-options {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
        }

        .analytics-filter-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .analytics-filter-option {
            display: block;
            cursor: pointer;
            margin: 0;
        }

        .analytics-filter-radio {
            display: none;
        }

        .analytics-filter-label {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.2s;
            font-size: 14px;
            justify-content: center;
        }

        .analytics-filter-label:hover {
            background: #e9ecef;
            border-color: #00a34e;
        }

        .analytics-filter-radio:checked+.analytics-filter-label {
            background: #e8f5e8;
            border-color: #00a34e;
            color: #00a34e;
            font-weight: 600;
        }

        .analytics-filter-icon {
            flex-shrink: 0;
        }

        /* Date Range */
        .analytics-date-range {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .analytics-date-input {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .analytics-date-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .analytics-date-field {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .analytics-date-field:focus {
            outline: none;
            border-color: #00a34e;
            box-shadow: 0 0 0 2px rgba(0, 163, 78, 0.1);
        }

        /* Filter Actions */
        .analytics-filter-actions {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }

        .analytics-filter-reset {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .analytics-filter-reset:hover {
            background: #5a6268;
        }

        .analytics-filter-apply {
            background: #00a34e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .analytics-filter-apply:hover {
            background: #008a42;
        }

        .filter-pading {
            padding: 4px;
        }

        .mr-10 {
            margin-right: 10px;
        }

        .filter-option-label {
            margin-bottom: 0;
            width: 100%;
        }

        #iconBox {
            top: .6rem;
            left: 19rem;
            /* box-shadow: -0.2px 0.05rem 0.2rem 0px rgba(0, 0, 0, 0.15) !important; */
            padding: 20px 13px 6px;
        }

        .arrow-top {
            position: absolute;
            top: 29px;
            left: 8px;
        }

        .md-header {
            background: background: rgb(5, 177, 48);
            background: linear-gradient(90deg, rgba(5, 177, 48, 1) 0%, rgba(3, 153, 39, 1) 57%, rgba(195, 223, 0, 1) 100%);
            color: white;
        }

        .form-check-input:checked {
            background-color: #00a34e;
            border-color: #00a34e;
        }

        .form-check-input:checked+label {

            color: #00a34e;
        }

        /* Removed duplicate .box-sta-filter to avoid conflicts */

        .filter-popup {
            display: block;
            width: 384px;
            max-height: 396px;
            overflow: initial;
            position: absolute;
            top: 198px;
            padding: 0;
            right: 357px;
            /* Changed from left: 0 */
            z-index: 999;
            background: white;
            padding: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
    </style>
    {{-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" /> --}}

    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />

    <!-- Custom Styles -->
    <style>
        body {
            font-family: "Inter", sans-serif;
            background-color: #f8f9fa;
            color: #495057;
        }

        .card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1),
                0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        .h-100-flex {
            height: 100%;
        }

        .main-header {
            font-size: 1.75rem;
            font-weight: 600;
        }

        .header-actions .btn {
            border-radius: 0.5rem;
        }

        .chart-card-header {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .gauge-chart-container {
            position: relative;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }

        .progress {
            height: 8px;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 3.5rem;
            font-weight: 700;
        }

        .head-seperator {
            border-top: 1px solid #dee2e6;
        }

        /* Styles for Top 5 Ticket Types */
        .ticket-types-card-body {
            background-image: radial-gradient(#dee2e6 1px, transparent 1px);
            background-size: 1.25rem 1.25rem;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .ticket-tree-container {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-grow: 1;
        }

        .ticket-tree-node {
            padding: 0.5rem 1rem;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            background-color: #fff;
            white-space: nowrap;
        }

        .ticket-tree-root {
            border-left: 5px solid #28a745;
        }

        .ticket-tree-branch {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            /* Vertical gap between child nodes */
        }

        .ticket-tree-child {
            border-left: 5px solid #28a745;
        }

        .sla-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
        }

        .sla-item p {
            margin: 0;
            font-size: 1rem;
            flex-basis: 40%;
        }

        .sla-item .stat-value {
            flex-basis: 20%;
            text-align: right;
        }

        .sla-item .chart-container {
            flex-basis: 50%;
            display: flex;
            justify-content: center;
        }

        .ticket-card {
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 1.5vh;
            height: auto;
        }

        .flow-container {
            position: relative;
            padding: 1.5vh 2vw;
            background: white;
            border-radius: 8px;
            min-height: 25vh;
            background-image: radial-gradient(circle,
                    #e0e0e0 1px,
                    rgba(0, 0, 0, 0) 1px);
            background-size: 20px 20px;
        }

        .flow-node {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 0.5vh 1.5vw;
            background: linear-gradient(to right, #28a745 10px, white 10px);
            color: #333;
            font-weight: 500;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .main-node {
            position: absolute;
            left: 4vw;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            width: fit-content;
        }

        .ticket-item {
            margin: 0.5vh 0;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            width: fit-content;
            position: relative;
            /* Added for absolute positioning of sub-flow */
        }

        .ticket-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transform: scale(1.02);
        }

        .sub-ticket {
            background: linear-gradient(to right, #28a745 10px, white 10px);
            /* Changed to green */
        }

        .sub-flow {
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            margin-left: 50px;
            width: auto;
        }

        .sub-list {
            display: flex;
            flex-direction: column;
        }

        .ticket-list {
            margin-left: 20vw;
            position: relative;
        }

        .expand-btn,
        .expand-sub-btn {
            background-color: #28a745;
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-left: 10px;
            font-size: 12px;
        }

        .expand-btn:hover,
        .expand-sub-btn:hover {
            background-color: #218838;
        }

        .connection-svg {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: visible;
        }

        .connection-path {
            fill: none;
            stroke: #6c757d;
            stroke-width: 3;
        }

        .modal-header {
            background-color: #28a745;
            color: white;
        }

        .close-modal {
            color: white;
            opacity: 1;
        }
    </style>

@endsection

@section('content')
    <livewire:tenant.supervisor.analytics.new-analytics />

@endsection



@section('script')
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Bootstrap JavaScript Bundle (includes Popper) -->
    {{-- <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script> --}}

    <!-- Wait for Bootstrap to load before initializing tooltips and other functions -->
    <script>
        // Ensure Bootstrap is loaded before proceeding
        function waitForBootstrap(callback) {
            if (typeof bootstrap !== 'undefined' || typeof window.bootstrap !== 'undefined') {
                callback();
            } else {
                setTimeout(() => waitForBootstrap(callback), 100);
            }
        }

        function setActive(selected) {
            document.querySelectorAll('.icons').forEach(icon => {
                icon.classList.remove('active');
                icon.querySelector('.arrow-top').classList.add('d-none');
            });

            selected.classList.add('active');
            selected.querySelector('.arrow-top').classList.remove('d-none');
        }

        // Initialize tooltips after Bootstrap is fully loaded
        waitForBootstrap(function() {
            const Bootstrap = window.bootstrap || bootstrap;
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new Bootstrap.Tooltip(
                tooltipTriggerEl));
        });
    </script>

    <!-- Custom Chart Scripts -->
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const chartJsDefaults = Chart.defaults;
            chartJsDefaults.font.family = "'Inter', sans-serif";

            // Received Trend Chart - Matching Figma Design
            const recivedCtx = document
                .getElementById("RecivedticketsChart")
                .getContext("2d");

            // Create gradient matching Figma design
            const trendGradient = recivedCtx.createLinearGradient(0, 0, 0, 256);
            trendGradient.addColorStop(0, "rgba(1, 164, 79, 1)"); // #01A44F at top
            trendGradient.addColorStop(1, "rgba(255, 255, 255, 1)"); // White at bottom

            // Initial labels - will be updated dynamically by Livewire based on date range
            const labelsNew = ["SAT", "SUN", "MON", "TUE", "WED", "THU", "FRI"];
            const dataNew = [0, 0, 0, 0, 0, 0, 0]; // Will be updated by Livewire with real data

            const Redata = {
                labels: labelsNew,
                datasets: [{
                    backgroundColor: trendGradient,
                    label: "Received",
                    data: dataNew,
                    borderColor: "#01A44F",
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 0,
                    pointHoverRadius: 6,
                    pointHoverBackgroundColor: "#01A44F",
                    pointHoverBorderColor: "#01A44F",
                    pointHoverBorderWidth: 2,
                }, ],
            };

            const Recconfig = {
                type: "line",
                data: Redata,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: {
                            left: 0,
                            right: 20,
                            top: 20,
                            bottom: 10,
                        },
                    },
                    scales: {
                        x: {
                            display: true, // Explicitly enable X-axis display
                            grid: {
                                display: false,
                                drawBorder: false,
                            },
                            ticks: {
                                display: true, // Explicitly enable tick display
                                color: "#7C8287",
                                font: {
                                    size: 12,
                                    family: "Inter",
                                },
                                padding: 8,
                                maxRotation: 0, // Keep labels horizontal
                                minRotation: 0,
                            },
                            border: {
                                display: false,
                            },
                        },
                        y: {
                            position: "left",
                            grid: {
                                display: false,
                                drawBorder: false,
                            },
                            ticks: {
                                color: "#7C8287",
                                font: {
                                    size: 12,
                                    family: "Inter",
                                },
                                callback: function(value) {
                                    return value;
                                },
                                padding: 8,
                                min: 0,
                                // Remove fixed max to allow dynamic scaling based on data
                            },
                            border: {
                                display: false,
                            },
                        },
                    },
                    plugins: {
                        filler: {
                            propagate: false,
                        },
                        title: {
                            display: false,
                        },
                        legend: {
                            display: false,
                        },
                        tooltip: {
                            enabled: true,
                            backgroundColor: "rgba(255, 255, 255, 0.95)",
                            titleColor: "#000",
                            bodyColor: "#000",
                            borderColor: "#E5E7EB",
                            borderWidth: 1,
                            cornerRadius: 4,
                            displayColors: false,
                            callbacks: {
                                title: function() {
                                    return "";
                                },
                                label: function(context) {
                                    return context.parsed.y;
                                },
                            },
                        },
                    },
                    interaction: {
                        intersect: false,
                        mode: "index",
                    },
                    elements: {
                        point: {
                            hoverRadius: 6,
                        },
                    },
                },
            };

            window.receivedTrendChart = new Chart(recivedCtx, Recconfig);

            // Debug: Test chart update with sample data
            console.log('Chart created:', window.receivedTrendChart);

            // Test function to manually update chart (for debugging)
            window.testChartUpdate = function() {
                const testData = [10, 20, 15, 25, 30, 18, 22];
                const testLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'];
                console.log('Testing chart update with data:', testData);
                console.log('Testing chart update with labels:', testLabels);

                if (window.receivedTrendChart) {
                    window.receivedTrendChart.data.datasets[0].data = testData;
                    window.receivedTrendChart.data.labels = testLabels;
                    window.receivedTrendChart.update('active');
                    console.log('Test update complete. Current labels:', window.receivedTrendChart.data.labels);
                } else {
                    console.error('Chart not found for test');
                }
            };

            // Test function to check chart state
            window.checkChartState = function() {
                if (window.receivedTrendChart) {
                    console.log('Chart state:', {
                        labels: window.receivedTrendChart.data.labels,
                        data: window.receivedTrendChart.data.datasets[0].data,
                        options: window.receivedTrendChart.options.scales.x
                    });
                } else {
                    console.error('Chart not found');
                }
            };

            // Global event listener for Livewire updates
            window.addEventListener('refreshReceivedTrendChart', function(event) {
                console.log('Event received:', event);
                const newData = event.detail;
                console.log('Updating main chart with data:', newData);

                // Debug: Log individual day values
                console.log('Individual day values:', {
                    sat: newData.sat,
                    sun: newData.sun,
                    mon: newData.mon,
                    tue: newData.tue,
                    wed: newData.wed,
                    thu: newData.thu,
                    fri: newData.fri
                });

                if (window.receivedTrendChart) {
                    const chartData = [
                        newData.sat, newData.sun, newData.mon, newData.tue,
                        newData.wed, newData.thu, newData.fri
                    ];

                    console.log('Chart data array:', chartData);
                    console.log('Chart grouping:', newData.grouping);
                    console.log('Chart labels:', newData.labels);
                    console.log('Max value in data:', Math.max(...chartData));

                    // Update chart data
                    window.receivedTrendChart.data.datasets[0].data = chartData;

                    // Update chart labels if provided
                    if (newData.labels && Array.isArray(newData.labels)) {
                        console.log('Updating main chart labels from:', window.receivedTrendChart.data
                            .labels, 'to:', newData.labels);
                        window.receivedTrendChart.data.labels = newData.labels;
                    } else {
                        console.log('No labels provided in main chart data, keeping existing:', window
                            .receivedTrendChart.data.labels);
                    }

                    // Force chart update with resize to ensure labels are redrawn
                    window.receivedTrendChart.resize();
                    window.receivedTrendChart.update('active');
                    console.log('Main chart updated successfully. Current labels:', window
                        .receivedTrendChart.data.labels);
                }
            });

            // Common options for small area charts
            const smallAreaChartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false,
                    },
                    tooltip: {
                        enabled: false,
                    },
                },
                scales: {
                    x: {
                        display: false,
                    },
                    y: {
                        display: false,
                    },
                },
                elements: {
                    point: {
                        radius: 0,
                    },
                },
            };

            // SLA Violated - Response Chart (Area Chart) - Matching Figma Design
            const slaResponseCtx = document
                .getElementById("slaResponseChart")
                .getContext("2d");
            // Create gradient matching Figma: Green gradient from top to transparent
            const responseGradient = slaResponseCtx.createLinearGradient(
                0,
                0,
                0,
                54
            );
            responseGradient.addColorStop(0, "rgba(73, 166, 119, 1)"); // #49A677 at 100% opacity
            responseGradient.addColorStop(1, "rgba(73, 166, 119, 0)"); // #49A677 at 0% opacity

            new Chart(slaResponseCtx, {
                type: "line",
                data: {
                    labels: [
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                    ],
                    datasets: [{
                        data: [
                            25, 35, 28, 45, 38, 50, 42, 35, 48, 40, 52, 38, 45, 35, 42,
                            48, 35, 40, 45, 38,
                        ],
                        borderColor: "#5DB68A", // Border color matching Figma stroke
                        borderWidth: 1,
                        tension: 0.4,
                        fill: true,
                        backgroundColor: responseGradient,
                        pointRadius: 0,
                        pointHoverRadius: 0,
                    }, ],
                },
                options: {
                    ...smallAreaChartOptions,
                    layout: {
                        padding: 0,
                    },
                },
            });

            // SLA Violated - Resolution Chart (Area Chart) - Matching Figma Design
            const slaResolutionCtx = document
                .getElementById("slaResolutionChart")
                .getContext("2d");
            // Create gradient matching Figma: Pink/Red gradient from top to transparent
            const resolutionGradient = slaResolutionCtx.createLinearGradient(
                0,
                0,
                0,
                61
            );
            resolutionGradient.addColorStop(0, "rgba(235, 148, 162, 1)"); // #EB94A2 at 100% opacity
            resolutionGradient.addColorStop(1, "rgba(235, 148, 162, 0)"); // #EB94A2 at 0% opacity

            new Chart(slaResolutionCtx, {
                type: "line",
                data: {
                    labels: [
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                        "",
                    ],
                    datasets: [{
                        data: [
                            30, 25, 40, 35, 28, 45, 38, 42, 35, 50, 40, 35, 42, 38, 45,
                            35, 40, 35, 42, 38,
                        ],
                        borderColor: "#EB7587", // Border color matching Figma stroke
                        borderWidth: 1,
                        tension: 0.4,
                        fill: true,
                        backgroundColor: resolutionGradient,
                        pointRadius: 0,
                        pointHoverRadius: 0,
                    }, ],
                },
                options: {
                    ...smallAreaChartOptions,
                    layout: {
                        padding: 0,
                    },
                },
            });

            // Common Doughnut Options
            const doughnutOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false,
                    },
                    tooltip: {
                        enabled: false,
                    },
                },
                cutout: "70%",
            };

            // Resolution SLA Performance Chart
            const resolutionSlaCtx = document
                .getElementById("resolutionSlaChart")
                .getContext("2d");
            new Chart(resolutionSlaCtx, {
                type: "doughnut",
                data: {
                    datasets: [{
                        data: [81, 19],
                        backgroundColor: ["#28a745", "#e9ecef"],
                        borderWidth: 0,
                    }, ],
                },
                options: {
                    ...doughnutOptions,
                    plugins: {
                        ...doughnutOptions.plugins,
                        title: {
                            display: true,
                            text: "81%",
                            position: "bottom",
                            font: {
                                size: 16,
                                weight: "bold",
                            },
                            color: "#000",
                            padding: {
                                top: -45,
                            },
                        },
                    },
                },
            });

            // Response SLA Performance Chart
            const responseSlaCtx = document
                .getElementById("responseSlaChart")
                .getContext("2d");
            new Chart(responseSlaCtx, {
                type: "doughnut",
                data: {
                    datasets: [{
                        data: [74, 26],
                        backgroundColor: ["#28a745", "#e9ecef"],
                        borderWidth: 0,
                    }, ],
                },
                options: {
                    ...doughnutOptions,
                    plugins: {
                        ...doughnutOptions.plugins,
                        title: {
                            display: true,
                            text: "74%",
                            position: "bottom",
                            font: {
                                size: 16,
                                weight: "bold",
                            },
                            color: "#000",
                            padding: {
                                top: -45,
                            },
                        },
                    },
                },
            });

            // Third Resolution SLA Chart
            const resolutionSlaCtx2 = document
                .getElementById("resolutionSlaChart2")
                .getContext("2d");
            new Chart(resolutionSlaCtx2, {
                type: "doughnut",
                data: {
                    datasets: [{
                        data: [66, 34],
                        backgroundColor: ["#28a745", "#e9ecef"],
                        borderWidth: 0,
                    }, ],
                },
                options: {
                    ...doughnutOptions,
                    plugins: {
                        ...doughnutOptions.plugins,
                        title: {
                            display: true,
                            text: "66%",
                            position: "bottom",
                            font: {
                                size: 16,
                                weight: "bold",
                            },
                            color: "#000",
                            padding: {
                                top: -45,
                            },
                        },
                    },
                },
            });

            // Custom plugin for gauge chart dot only - Matching Figma Design
            const gaugeDotPlugin = {
                id: "gaugeDot",
                afterDraw: (chart) => {
                    const {
                        ctx,
                        data,
                        options
                    } = chart;
                    const {
                        dotColor,
                        percentage
                    } = options.plugins.gaugeDot;

                    ctx.save();

                    // Get the actual arc from the chart to position dot correctly
                    const meta = chart.getDatasetMeta(0);
                    const arc = meta.data[0];
                    if (!arc) return;

                    // Position dot at the END of the first (colored) arc segment
                    const targetAngle = arc.endAngle; // This is the end of the colored portion

                    // Calculate dot position on the arc at the tip of the colored section
                    const radius = (arc.outerRadius + arc.innerRadius) / 2;
                    const dotX = arc.x + Math.cos(targetAngle) * radius;
                    const dotY = arc.y + Math.sin(targetAngle) * radius;

                    // Draw the indicator dot with shadow
                    ctx.shadowColor = "rgba(13, 10, 44, 0.08)";
                    ctx.shadowBlur = 6;
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 4;

                    // Draw white border
                    ctx.fillStyle = "#fff";
                    ctx.beginPath();
                    ctx.arc(dotX, dotY, 7, 0, Math.PI * 2);
                    ctx.closePath();
                    ctx.fill();

                    // Reset shadow
                    ctx.shadowColor = "transparent";
                    ctx.shadowBlur = 0;
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 0;

                    // Draw colored center
                    ctx.fillStyle = dotColor;
                    ctx.beginPath();
                    ctx.arc(dotX, dotY, 3, 0, Math.PI * 2);
                    ctx.closePath();
                    ctx.fill();

                    ctx.restore();
                },
            };

            // Common options for gauge charts - Matching Figma Design
            const gaugeOptions = (dotColor, percentage) => ({
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false,
                    },
                    tooltip: {
                        enabled: false,
                    },
                    gaugeDot: {
                        dotColor,
                        percentage,
                    },
                },
                rotation: -90,
                circumference: 180,
                cutout: "85%",
                elements: {
                    arc: {
                        borderWidth: 0,
                    },
                },
            });

            // Average Response Time Gauge Chart - Red/Pink Color (Matching Figma)
            const avgResponseTimeCtx = document
                .getElementById("avgResponseTimeChart")
                .getContext("2d");
            new Chart(avgResponseTimeCtx, {
                type: "doughnut",
                data: {
                    datasets: [{
                        data: [35, 65], // 35% filled, 65% empty
                        backgroundColor: ["#FF5D60",
                            "#E5EAFC"
                        ], // Red section and light blue background
                        borderWidth: 0,
                    }, ],
                },
                options: gaugeOptions("#FF5D60", 35),
                plugins: [gaugeDotPlugin],
            });

            // Aggregate Ticket Resolution Time Gauge Chart - Green Color (Matching Figma)
            const ticketResolutionTimeCtx = document
                .getElementById("ticketResolutionTimeChart")
                .getContext("2d");
            new Chart(ticketResolutionTimeCtx, {
                type: "doughnut",
                data: {
                    datasets: [{
                        data: [65, 35], // 65% filled, 35% empty
                        backgroundColor: ["#01A44F",
                            "#E5EAFC"
                        ], // Green section and light blue background
                        borderWidth: 0,
                    }, ],
                },
                options: gaugeOptions("#01A44F", 65),
                plugins: [gaugeDotPlugin],
            });
        });
    </script>

    <div class="modal fade" id="expandedModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Top 10 Ticket Types</h5>
                    <button type="button" class="btn-close close-modal" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="flow-container" id="modalFlowContainer" style="min-height: 600px">
                        <div class="main-node flow-node">Top 10 Tickets</div>
                        <svg class="connection-svg" id="modalConnectionSvg"></svg>
                        <div class="ticket-list" id="modalTicketList"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const ticketData = [{
                id: 1,
                name: "Ticket 1",
                children: [{
                        id: 11,
                        name: "Sub-Ticket 1.1"
                    },
                    {
                        id: 12,
                        name: "Sub-Ticket 1.2"
                    },
                ],
            },
            {
                id: 2,
                name: "Ticket 2",
                children: [{
                    id: 21,
                    name: "Sub-Ticket 2.1"
                }],
            },
            {
                id: 3,
                name: "Ticket 3"
            },
            {
                id: 4,
                name: "Ticket 4"
            },
            {
                id: 5,
                name: "Ticket 5",
                children: [{
                        id: 51,
                        name: "Sub-Ticket 5.1"
                    },
                    {
                        id: 52,
                        name: "Sub-Ticket 5.2"
                    },
                    {
                        id: 53,
                        name: "Sub-Ticket 5.3"
                    },
                ],
            },
            {
                id: 6,
                name: "Ticket 6"
            },
            {
                id: 7,
                name: "Ticket 7"
            },
            {
                id: 8,
                name: "Ticket 8"
            },
            {
                id: 9,
                name: "Ticket 9"
            },
            {
                id: 10,
                name: "Ticket 10"
            },
        ];

        function createTicketItem(ticket, isSub = false) {
            const subClass = isSub ? "sub-ticket" : "";
            let html = `
                <div class="ticket-item flow-node ${subClass}">
                    <strong>${ticket.name}</strong>
            `;
            if (ticket.children && ticket.children.length > 0) {
                html += `<button class="expand-sub-btn" onclick="toggleSubFlow(this)">+</button>`;
                html +=
                    `<div class="sub-flow" style="display: none;"><svg class="connection-svg sub-svg"></svg><div class="sub-list"></div></div>`;
            }
            html += `</div>`;
            return html;
        }

        function toggleSubFlow(button) {
            const ticketDiv = button.parentElement;
            const subFlowDiv = ticketDiv.querySelector(".sub-flow");
            const subList = subFlowDiv.querySelector(".sub-list");
            const subSvg = subFlowDiv.querySelector(".sub-svg");
            const ticketName = ticketDiv.querySelector("strong").textContent;
            const ticket = ticketData.find((t) => t.name === ticketName) || {}; // Find ticket data

            if (subFlowDiv.style.display === "none") {
                subList.innerHTML = ticket.children
                    .map((child) => createTicketItem(child, true))
                    .join("");
                subFlowDiv.style.display = "block";
                button.textContent = "-";
                setTimeout(() => drawSubConnections(subSvg, ticketDiv, subList), 100);
            } else {
                subFlowDiv.style.display = "none";
                button.textContent = "+";
            }
            // Redraw main connections
            const svgId = subFlowDiv
                .closest(".flow-container")
                .querySelector(".connection-svg").id;
            drawConnections(
                svgId,
                document.querySelectorAll(".ticket-item:not(.sub-ticket)").length
            );
        }

        function drawConnections(svgId, ticketCount) {
            const svg = document.getElementById(svgId);
            const mainNode = svg.previousElementSibling;
            const ticketList = svg.nextElementSibling;
            const markerId = `arrowhead-${svgId}`;

            svg.innerHTML = `
                <defs>
                    <marker id="${markerId}" viewBox="0 0 10 10" refX="9" refY="5"
                        markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                        <path d="M 0 0 L 9 5 L 0 10" fill="none" stroke="#6c757d" stroke-width="1.5"></path>
                    </marker>
                </defs>
            `;

            const mainRect = mainNode.getBoundingClientRect();
            const containerRect = svg.parentElement.getBoundingClientRect();
            const tickets = ticketList.querySelectorAll(
                ".ticket-item:not(.sub-ticket)"
            );

            const mainX = mainRect.right - containerRect.left;
            const mainY = mainRect.top + mainRect.height / 2 - containerRect.top;

            tickets.forEach((ticket) => {
                const ticketRect = ticket.getBoundingClientRect();
                const ticketX = ticketRect.left - containerRect.left;
                const ticketY =
                    ticketRect.top + ticketRect.height / 2 - containerRect.top;

                const path = document.createElementNS(
                    "http://www.w3.org/2000/svg",
                    "path"
                );
                const midX = mainX + 40;
                const d = `M ${mainX} ${mainY} C ${midX} ${mainY}, ${midX} ${ticketY}, ${ticketX} ${ticketY}`;
                path.setAttribute("d", d);
                path.setAttribute("class", "connection-path");
                path.setAttribute("marker-end", `url(#${markerId})`);
                svg.appendChild(path);
            });
        }

        function drawSubConnections(subSvg, parentTicket, subList) {
            const markerId = `sub-arrowhead-${Date.now()}`; // Unique ID
            subSvg.innerHTML = `
                <defs>
                    <marker id="${markerId}" viewBox="0 0 10 10" refX="9" refY="5"
                        markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                        <path d="M 0 0 L 9 5 L 0 10" fill="none" stroke="#6c757d" stroke-width="1.5"></path>
                    </marker>
                </defs>
            `;

            const parentRect = parentTicket.getBoundingClientRect();
            const containerRect = subSvg.parentElement.getBoundingClientRect();
            const subTickets = subList.querySelectorAll(".sub-ticket");

            const parentX = parentRect.right - containerRect.left;
            const parentY =
                parentRect.top + parentRect.height / 2 - containerRect.top;

            subTickets.forEach((sub) => {
                const subRect = sub.getBoundingClientRect();
                const subX = subRect.left - containerRect.left;
                const subY = subRect.top + subRect.height / 2 - containerRect.top;

                const path = document.createElementNS(
                    "http://www.w3.org/2000/svg",
                    "path"
                );
                const midX = parentX + 30;
                const d = `M ${parentX} ${parentY} C ${midX} ${parentY}, ${midX} ${subY}, ${subX} ${subY}`;
                path.setAttribute("d", d);
                path.setAttribute("class", "connection-path");
                path.setAttribute("marker-end", `url(#${markerId})`);
                subSvg.appendChild(path);
            });
        }

        function renderTickets(containerId, svgId, limit = 5) {
            const container = document.getElementById(containerId);
            const ticketsToShow = ticketData.slice(0, limit);

            container.innerHTML = ticketsToShow
                .map((ticket) => createTicketItem(ticket))
                .join("");

            setTimeout(() => {
                drawConnections(svgId, limit);
            }, 100);
        }

        function expandView() {
            try {
                // Wait a bit more for Bootstrap to be fully loaded
                setTimeout(function() {
                    // Try multiple ways to access Bootstrap Modal
                    let BootstrapModal = null;

                    if (window.bootstrap && window.bootstrap.Modal) {
                        BootstrapModal = window.bootstrap.Modal;
                    } else if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                        BootstrapModal = bootstrap.Modal;
                    } else if (typeof Modal !== 'undefined') {
                        // Fallback to global Modal if available
                        BootstrapModal = Modal;
                    } else {
                        // Try to find it in global scope
                        BootstrapModal = window.Modal || window.Bootstrap?.Modal;
                    }

                    if (!BootstrapModal) {
                        console.error('Bootstrap Modal not found. Available properties:', {
                            bootstrap: typeof bootstrap !== 'undefined' ? Object.keys(bootstrap) :
                                'undefined',
                            windowBootstrap: typeof window.bootstrap !== 'undefined' ? Object.keys(window
                                .bootstrap) : 'undefined'
                        });
                        // Fallback: try to show modal using jQuery or direct manipulation
                        const modalEl = document.getElementById("expandedModal");
                        if (modalEl) {
                            modalEl.style.display = 'block';
                            modalEl.classList.add('show');
                            document.body.classList.add('modal-open');

                            // Create backdrop
                            const backdrop = document.createElement('div');
                            backdrop.className = 'modal-backdrop fade show';
                            backdrop.id = 'modal-backdrop';
                            document.body.appendChild(backdrop);

                            renderTickets("modalTicketList", "modalConnectionSvg", 10);
                            setTimeout(() => drawConnections("modalConnectionSvg", 10), 100);

                            // Add close functionality
                            const closeBtn = modalEl.querySelector('.btn-close');
                            const closeModal = () => {
                                modalEl.style.display = 'none';
                                modalEl.classList.remove('show');
                                document.body.classList.remove('modal-open');
                                document.getElementById('modal-backdrop')?.remove();
                            };

                            if (closeBtn) {
                                closeBtn.onclick = closeModal;
                            }
                            backdrop.onclick = closeModal;
                        }
                        return;
                    }

                    const modalElement = document.getElementById("expandedModal");
                    if (!modalElement) {
                        console.error('Modal element not found');
                        return;
                    }

                    const modal = new BootstrapModal(modalElement);
                    renderTickets("modalTicketList", "modalConnectionSvg", 10);
                    modal.show();

                    modalElement.addEventListener("shown.bs.modal", function() {
                        drawConnections("modalConnectionSvg", 10);
                    });
                }, 100);
            } catch (error) {
                console.error('Error in expandView:', error);
            }
        }

        document.addEventListener("DOMContentLoaded", function() {
            // Debug Bootstrap availability
            console.log('Bootstrap debug info:', {
                bootstrap: typeof bootstrap,
                windowBootstrap: typeof window.bootstrap,
                bootstrapModal: typeof bootstrap !== 'undefined' ? typeof bootstrap.Modal : 'N/A',
                windowBootstrapModal: typeof window.bootstrap !== 'undefined' ? typeof window.bootstrap
                    .Modal : 'N/A'
            });

            renderTickets("ticketList", "connectionSvg", 5);

            window.addEventListener("resize", function() {
                drawConnections("connectionSvg", 5);
                if (
                    document.getElementById("expandedModal").classList.contains("show")
                ) {
                    drawConnections("modalConnectionSvg", 10);
                }
                // Redraw open sub-connections if any
                document
                    .querySelectorAll('.sub-flow[style*="block"]')
                    .forEach((subFlow) => {
                        const subSvg = subFlow.querySelector(".sub-svg");
                        const parentTicket = subFlow.parentElement;
                        const subList = subFlow.querySelector(".sub-list");
                        drawSubConnections(subSvg, parentTicket, subList);
                    });
            });
        });
    </script>
    <script>
        // Analytics Date Filter - Improved with better UX
        window.AnalyticsDateFilter = {
            toggle: function() {
                const menu = document.getElementById('analyticsDateOptions');
                const arrow = document.querySelector('.analytics-dropdown-arrow');
                if (menu) {
                    menu.classList.toggle('show');
                    if (arrow) {
                        arrow.style.transform = menu.classList.contains('show') ? 'rotate(180deg)' : 'rotate(0deg)';
                    }
                }
            },

            close: function() {
                const menu = document.getElementById('analyticsDateOptions');
                const arrow = document.querySelector('.analytics-dropdown-arrow');
                if (menu) {
                    menu.classList.remove('show');
                    if (arrow) {
                        arrow.style.transform = 'rotate(0deg)';
                    }
                }
            },

            init: function() {
                // Close dropdown when clicking outside
                document.addEventListener('click', function(event) {
                    const dropdown = document.querySelector('.analytics-date-dropdown');
                    const menu = document.getElementById('analyticsDateOptions');

                    if (dropdown && menu && !dropdown.contains(event.target)) {
                        window.AnalyticsDateFilter.close();
                    }
                });

                // Close dropdown when pressing Escape
                document.addEventListener('keydown', function(event) {
                    if (event.key === 'Escape') {
                        window.AnalyticsDateFilter.close();
                    }
                });

                // Add loading state management
                window.addEventListener('livewire:load', function() {
                    console.log('Analytics filters initialized');
                });

                // Listen for Livewire updates
                window.addEventListener('livewire:update', function() {
                    // Update social media filter state based on channel selection
                    const socialMediaSection = document.querySelector(
                        '.analytics-filter-section[style*="opacity"]');
                    if (socialMediaSection) {
                        // This will be handled by Livewire re-rendering
                    }
                });
            }
        };

        // Legacy function for compatibility
        function toggleOptions2() {
            window.AnalyticsDateFilter.toggle();
        }

        // Initialize filters when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            window.AnalyticsDateFilter.init();
        });

        function expandView() {
            // Expand functionality
            console.log('Expand view clicked');
        }

        // Analytics Chart Manager - Livewire Compatible
        window.AnalyticsChartManager = {
            charts: {},
            isInitialized: false,

            // Initialize all charts
            init: function() {
                if (this.isInitialized) {
                    this.destroyAllCharts();
                }

                console.log('Initializing Analytics Chart Manager...');
                this.initReceivedTrendChart();
                this.initAllOtherCharts();
                this.isInitialized = true;
            },

            // Destroy all charts to prevent memory leaks
            destroyAllCharts: function() {
                Object.keys(this.charts).forEach(key => {
                    if (this.charts[key] && typeof this.charts[key].destroy === 'function') {
                        this.charts[key].destroy();
                        delete this.charts[key];
                    }
                });
                console.log('All charts destroyed');
            },

            // Initialize the main received trend chart
            initReceivedTrendChart: function() {
                const canvas = document.getElementById('RecivedticketsChart');
                if (!canvas || typeof Chart === 'undefined') {
                    console.warn('Chart.js not loaded or canvas not found');
                    return;
                }

                const ctx = canvas.getContext('2d');

                // Destroy existing chart if it exists
                if (this.charts.receivedTrend) {
                    this.charts.receivedTrend.destroy();
                }

                // Create gradient
                const trendGradient = ctx.createLinearGradient(0, 0, 0, 256);
                trendGradient.addColorStop(0, "rgba(1, 164, 79, 1)");
                trendGradient.addColorStop(1, "rgba(255, 255, 255, 1)");

                const config = {
                    type: 'line',
                    data: {
                        labels: ["SAT", "SUN", "MON", "TUE", "WED", "THU", "FRI"],
                        datasets: [{
                            backgroundColor: trendGradient,
                            label: "Received",
                            data: [0, 0, 0, 0, 0, 0, 0],
                            borderColor: "#01A44F",
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 0,
                            pointHoverRadius: 6,
                            pointHoverBackgroundColor: "#01A44F",
                            pointHoverBorderColor: "#01A44F",
                            pointHoverBorderWidth: 2,
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        layout: {
                            padding: {
                                left: 0,
                                right: 20,
                                top: 20,
                                bottom: 10
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false,
                                    drawBorder: false
                                },
                                ticks: {
                                    color: "#7C8287",
                                    font: {
                                        size: 12,
                                        family: "Inter"
                                    },
                                    padding: 8
                                },
                                border: {
                                    display: false
                                }
                            },
                            y: {
                                position: "left",
                                grid: {
                                    display: false,
                                    drawBorder: false
                                },
                                ticks: {
                                    color: "#7C8287",
                                    font: {
                                        size: 12,
                                        family: "Inter"
                                    },
                                    callback: function(value) {
                                        return value;
                                    },
                                    padding: 8,
                                    stepSize: 250,
                                    min: 0,
                                    max: 500
                                },
                                border: {
                                    display: false
                                }
                            }
                        },
                        plugins: {
                            filler: {
                                propagate: false
                            },
                            title: {
                                display: false
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                enabled: true,
                                backgroundColor: "rgba(255, 255, 255, 0.95)",
                                titleColor: "#000",
                                bodyColor: "#000",
                                borderColor: "#E5E7EB",
                                borderWidth: 1,
                                cornerRadius: 4,
                                displayColors: false,
                                callbacks: {
                                    title: function() {
                                        return "";
                                    },
                                    label: function(context) {
                                        return context.parsed.y;
                                    }
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: "index"
                        },
                        elements: {
                            point: {
                                hoverRadius: 6
                            }
                        }
                    }
                };

                this.charts.receivedTrend = new Chart(ctx, config);
                console.log('Received trend chart initialized');
            },

            // Update the received trend chart with new data
            updateReceivedTrendChart: function(newData) {
                if (this.charts.receivedTrend && newData) {
                    console.log('Updating received trend chart with data:', newData);
                    this.charts.receivedTrend.data.datasets[0].data = [
                        newData.sat || 0, newData.sun || 0, newData.mon || 0, newData.tue || 0,
                        newData.wed || 0, newData.thu || 0, newData.fri || 0
                    ];
                    this.charts.receivedTrend.update('none');
                    console.log('Chart updated successfully');
                } else {
                    console.warn('Chart not found or no data provided for update');
                }
            },

            // Initialize all other charts (SLA, Doughnut, Gauge charts)
            initAllOtherCharts: function() {
                this.initSLACharts();
                this.initDoughnutCharts();
                this.initGaugeCharts();
            },

            // Initialize SLA violation charts
            initSLACharts: function() {
                // SLA Response Chart
                const slaResponseCanvas = document.getElementById('slaResponseChart');
                if (slaResponseCanvas) {
                    const ctx = slaResponseCanvas.getContext('2d');
                    const responseGradient = ctx.createLinearGradient(0, 0, 0, 54);
                    responseGradient.addColorStop(0, "rgba(73, 166, 119, 1)");
                    responseGradient.addColorStop(1, "rgba(73, 166, 119, 0)");

                    this.charts.slaResponse = new Chart(ctx, {
                        type: "line",
                        data: {
                            labels: Array(20).fill(""),
                            datasets: [{
                                data: [25, 35, 28, 45, 38, 50, 42, 35, 48, 40, 52, 38, 45, 35, 42,
                                    48, 35, 40, 45, 38
                                ],
                                borderColor: "#5DB68A",
                                borderWidth: 1,
                                tension: 0.4,
                                fill: true,
                                backgroundColor: responseGradient,
                                pointRadius: 0,
                                pointHoverRadius: 0,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    enabled: false
                                }
                            },
                            scales: {
                                x: {
                                    display: false
                                },
                                y: {
                                    display: false
                                }
                            },
                            elements: {
                                point: {
                                    radius: 0
                                }
                            },
                            layout: {
                                padding: 0
                            }
                        }
                    });
                }

                // SLA Resolution Chart
                const slaResolutionCanvas = document.getElementById('slaResolutionChart');
                if (slaResolutionCanvas) {
                    const ctx = slaResolutionCanvas.getContext('2d');
                    const resolutionGradient = ctx.createLinearGradient(0, 0, 0, 61);
                    resolutionGradient.addColorStop(0, "rgba(235, 148, 162, 1)");
                    resolutionGradient.addColorStop(1, "rgba(235, 148, 162, 0)");

                    this.charts.slaResolution = new Chart(ctx, {
                        type: "line",
                        data: {
                            labels: Array(20).fill(""),
                            datasets: [{
                                data: [30, 25, 40, 35, 28, 45, 38, 42, 35, 50, 40, 35, 42, 38, 45,
                                    35, 40, 35, 42, 38
                                ],
                                borderColor: "#EB7587",
                                borderWidth: 1,
                                tension: 0.4,
                                fill: true,
                                backgroundColor: resolutionGradient,
                                pointRadius: 0,
                                pointHoverRadius: 0,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    enabled: false
                                }
                            },
                            scales: {
                                x: {
                                    display: false
                                },
                                y: {
                                    display: false
                                }
                            },
                            elements: {
                                point: {
                                    radius: 0
                                }
                            },
                            layout: {
                                padding: 0
                            }
                        }
                    });
                }
            },

            // Initialize doughnut charts
            initDoughnutCharts: function() {
                const doughnutOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    cutout: "70%"
                };

                // Resolution SLA Performance Chart
                const resolutionSlaCanvas = document.getElementById('resolutionSlaChart');
                if (resolutionSlaCanvas) {
                    this.charts.resolutionSla = new Chart(resolutionSlaCanvas, {
                        type: "doughnut",
                        data: {
                            datasets: [{
                                data: [81, 19],
                                backgroundColor: ["#28a745", "#e9ecef"],
                                borderWidth: 0,
                            }]
                        },
                        options: {
                            ...doughnutOptions,
                            plugins: {
                                ...doughnutOptions.plugins,
                                title: {
                                    display: true,
                                    text: "81%",
                                    position: "bottom",
                                    font: {
                                        size: 16,
                                        weight: "bold"
                                    },
                                    color: "#000",
                                    padding: {
                                        top: -45
                                    }
                                }
                            }
                        }
                    });
                }

                // Response SLA Performance Chart
                const responseSlaCanvas = document.getElementById('responseSlaChart');
                if (responseSlaCanvas) {
                    this.charts.responseSla = new Chart(responseSlaCanvas, {
                        type: "doughnut",
                        data: {
                            datasets: [{
                                data: [74, 26],
                                backgroundColor: ["#28a745", "#e9ecef"],
                                borderWidth: 0,
                            }]
                        },
                        options: {
                            ...doughnutOptions,
                            plugins: {
                                ...doughnutOptions.plugins,
                                title: {
                                    display: true,
                                    text: "74%",
                                    position: "bottom",
                                    font: {
                                        size: 16,
                                        weight: "bold"
                                    },
                                    color: "#000",
                                    padding: {
                                        top: -45
                                    }
                                }
                            }
                        }
                    });
                }

                // Third Resolution SLA Chart
                const resolutionSla2Canvas = document.getElementById('resolutionSlaChart2');
                if (resolutionSla2Canvas) {
                    this.charts.resolutionSla2 = new Chart(resolutionSla2Canvas, {
                        type: "doughnut",
                        data: {
                            datasets: [{
                                data: [66, 34],
                                backgroundColor: ["#28a745", "#e9ecef"],
                                borderWidth: 0,
                            }]
                        },
                        options: {
                            ...doughnutOptions,
                            plugins: {
                                ...doughnutOptions.plugins,
                                title: {
                                    display: true,
                                    text: "66%",
                                    position: "bottom",
                                    font: {
                                        size: 16,
                                        weight: "bold"
                                    },
                                    color: "#000",
                                    padding: {
                                        top: -45
                                    }
                                }
                            }
                        }
                    });
                }
            },

            // Initialize gauge charts
            initGaugeCharts: function() {
                // Gauge chart plugin for dots
                const gaugeDotPlugin = {
                    id: "gaugeDot",
                    afterDraw: (chart) => {
                        const {
                            ctx,
                            options
                        } = chart;
                        const {
                            dotColor
                        } = options.plugins.gaugeDot;

                        ctx.save();
                        const meta = chart.getDatasetMeta(0);
                        const arc = meta.data[0];
                        if (!arc) return;

                        const targetAngle = arc.endAngle;
                        const radius = (arc.outerRadius + arc.innerRadius) / 2;
                        const dotX = arc.x + Math.cos(targetAngle) * radius;
                        const dotY = arc.y + Math.sin(targetAngle) * radius;

                        // Draw white border
                        ctx.fillStyle = "#fff";
                        ctx.beginPath();
                        ctx.arc(dotX, dotY, 7, 0, Math.PI * 2);
                        ctx.fill();

                        // Draw colored center
                        ctx.fillStyle = dotColor;
                        ctx.beginPath();
                        ctx.arc(dotX, dotY, 3, 0, Math.PI * 2);
                        ctx.fill();
                        ctx.restore();
                    }
                };

                const gaugeOptions = (dotColor) => ({
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        },
                        gaugeDot: {
                            dotColor
                        }
                    },
                    rotation: -90,
                    circumference: 180,
                    cutout: "85%",
                    elements: {
                        arc: {
                            borderWidth: 0
                        }
                    }
                });

                // Average Response Time Gauge
                const avgResponseCanvas = document.getElementById('avgResponseTimeChart');
                if (avgResponseCanvas) {
                    this.charts.avgResponse = new Chart(avgResponseCanvas, {
                        type: "doughnut",
                        data: {
                            datasets: [{
                                data: [35, 65],
                                backgroundColor: ["#FF5D60", "#E5EAFC"],
                                borderWidth: 0,
                            }]
                        },
                        options: gaugeOptions("#FF5D60"),
                        plugins: [gaugeDotPlugin]
                    });
                }

                // Ticket Resolution Time Gauge
                const ticketResolutionCanvas = document.getElementById('ticketResolutionTimeChart');
                if (ticketResolutionCanvas) {
                    this.charts.ticketResolution = new Chart(ticketResolutionCanvas, {
                        type: "doughnut",
                        data: {
                            datasets: [{
                                data: [65, 35],
                                backgroundColor: ["#01A44F", "#E5EAFC"],
                                borderWidth: 0,
                            }]
                        },
                        options: gaugeOptions("#01A44F"),
                        plugins: [gaugeDotPlugin]
                    });
                }
            }
        };

        // Initialize everything when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing analytics...');

            // Set Chart.js defaults
            if (typeof Chart !== 'undefined') {
                Chart.defaults.font.family = "'Inter', sans-serif";
                window.AnalyticsChartManager.init();
            } else {
                console.error('Chart.js not loaded');
            }
        });

        // Livewire event listeners for chart updates
        document.addEventListener('livewire:load', function() {
            console.log('Livewire loaded, setting up event listeners...');

            if (typeof Livewire !== 'undefined') {
                // Test event listener to verify Livewire events are working
                Livewire.on('testEvent', function(message) {
                    console.log('TEST EVENT RECEIVED:', message);
                    alert('Livewire events are working! Message: ' + message);
                });

                Livewire.on('refreshReceivedTrendChart', function(data) {
                    console.log('Livewire event received for chart update:', data);

                    // Try multiple ways to find the chart
                    let chart = window.receivedTrendChart ||
                        window.AnalyticsChartManager?.charts?.receivedTrendChart ||
                        Chart.getChart('RecivedticketsChart');

                    if (chart) {
                        const chartData = [
                            data.sat, data.sun, data.mon, data.tue,
                            data.wed, data.thu, data.fri
                        ];

                        console.log('Updating chart via Livewire event:', chartData);
                        console.log('Chart grouping:', data.grouping);
                        console.log('Chart labels:', data.labels);
                        console.log('Max value in data:', Math.max(...chartData));

                        // Update chart data
                        chart.data.datasets[0].data = chartData;

                        // Update chart labels if provided
                        if (data.labels && Array.isArray(data.labels)) {
                            console.log('Updating chart labels from:', chart.data.labels, 'to:', data
                                .labels);
                            chart.data.labels = data.labels;
                        } else {
                            console.log('No labels provided in data, keeping existing:', chart.data.labels);
                        }

                        // Force chart update with resize to ensure labels are redrawn
                        chart.resize();
                        chart.update('active');
                        console.log('Chart updated successfully via Livewire. Current labels:', chart.data
                            .labels);
                    } else {
                        console.error('Chart not found. Available charts:', {
                            receivedTrendChart: window.receivedTrendChart,
                            chartManager: window.AnalyticsChartManager,
                            chartById: Chart.getChart('RecivedticketsChart')
                        });
                    }
                });
            } else {
                console.error('Livewire not available');
            }
        });

        // Livewire event handlers - Prevent multiple initializations
        let chartsInitialized = false;

        document.addEventListener('livewire:load', function() {
            console.log('Livewire loaded, checking if charts need initialization...');
            if (typeof Chart !== 'undefined' && window.AnalyticsChartManager && !chartsInitialized) {
                window.AnalyticsChartManager.init();
                chartsInitialized = true;
            }
        });

        document.addEventListener('livewire:update', function() {
            console.log('Livewire updated, skipping chart re-initialization to prevent conflicts...');
            // Don't re-initialize charts on every update to prevent conflicts
            // Charts will be updated via Livewire events instead
        });

        // Listen for Livewire events to update chart data
        window.addEventListener('refreshReceivedTrendChart', function(event) {
            const newData = event.detail;
            console.log('Received chart update event:', newData);
            if (window.AnalyticsChartManager) {
                window.AnalyticsChartManager.updateReceivedTrendChart(newData);
            }
        });
    </script>

@endsection
